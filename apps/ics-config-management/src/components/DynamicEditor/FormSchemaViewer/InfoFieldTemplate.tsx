import React, { useState, createContext, useContext } from 'react';
import {
  FieldErrorProps,
  FieldTemplateProps,
  ObjectFieldTemplateProps,
  ArrayFieldTemplateItemType,
  ArrayFieldTemplateProps,
} from '@rjsf/utils';
import HelpIcon from '@mui/icons-material/Help';
import { IconButton, Tooltip, Box, Button } from '@mui/material';
import { ExpandLess, ExpandMore, NorthOutlined, SouthOutlined, DeleteOutlineOutlined, Add } from '@mui/icons-material';
import './InfoFieldTemplate.css';

// Context for passing array item buttons functionality
const ArrayItemContext = createContext<{
  removeButton?: React.ReactNode;
  moveUpButton?: React.ReactNode;
  moveDownButton?: React.ReactNode;
}>({});

const customBorder = {
  borderBottom: '1px solid lightgray',
};

const customObjectStyle = {
  backgroundColor: '#F0F0F0',
  padding: '24px 24px 32px 24px',
  borderRadius:'12px'
};

const customLabelStyles = {
  fontSize: '20px',
  fontWeight: 'normal',
};


const customListItemStyles = {
  whiteSpace: 'nowrap',
};

const CustomFieldTemplate: React.FC<FieldTemplateProps> = ({
  id,
  classNames,
  label,
  required,
  description,
  errors,
  children,
  schema,
  rawDescription,
}) => {
  const descriptionForNestedProperties = description.props.description;

  return (
    <div
      style={schema.type === 'array' || schema.type === 'object' ? customObjectStyle : null}
      className={classNames}
    >
      <label
        style={
          schema.type === 'array' || schema.type === 'object'
            ? customLabelStyles
            : null
        }
        htmlFor={id}
      >
        {label}
        {required ? <span style={{ color: 'red' }}>*</span> : null}
        {descriptionForNestedProperties !== '' ? (
          <Tooltip title={rawDescription}>
            <IconButton aria-label='info' size='small'>
              <HelpIcon fontSize='small' />
            </IconButton>
          </Tooltip>
        ) : null}
      </label>
      <span
        className={schema?.readOnly ? 'disabled' : ''}
        title={schema.readOnly ? 'Read-only field' : ''}
      >
        {children}
      </span>
      {errors}
    </div>
  );
};

const CustomObjectFieldTemplate: React.FC<ObjectFieldTemplateProps> = ({
  idSchema,
  properties,
  schema
}) => {
  const [isExpanded, setIsExpanded] = useState<boolean>(true);
  const { removeButton, moveUpButton, moveDownButton } = useContext(ArrayItemContext);

  if (!properties.length) return null;

  // Check if this container has any direct input fields
  // If it has any direct fields (string, number, boolean, etc.), it should get white background
  const hasDirectFields = properties.some(prop => {
    // Access the schema from the property's content props
    const propSchema = prop.content?.props?.schema;
    // Check if this property is a primitive field type
    return propSchema?.type && ['string', 'number', 'boolean', 'integer'].includes(propSchema.type);
  });

  // Use white background for containers with any direct fields, gray for containers with only nested objects
  const containerClass = hasDirectFields ? 'card-shadow-white' : 'card-shadow';

  console.log('Properties:', properties.map(p => ({
    name: p.name,
    type: p.content?.props?.schema?.type,
    hasProperties: !!p.content?.props?.schema?.properties,
    propertiesCount: p.content?.props?.schema?.properties ? Object.keys(p.content?.props?.schema?.properties).length : 0
  })), 'hasDirectFields:', hasDirectFields, 'schema:', schema);

  return (
    <div className={containerClass}>
      <span id={idSchema.$id} />

      {/* Header area with buttons */}
      <div style={{ marginBottom: '8px', overflow: 'hidden' }}>
        {/* Move buttons positioned on the left side */}
        {(moveUpButton || moveDownButton) && (
          <span style={{ float: 'left', display: 'flex', alignItems: 'center' }}>
            {moveUpButton}
            {moveDownButton}
          </span>
        )}

        {/* Remove button and expand/collapse arrow positioned on the right side */}
        <span style={{ float: 'right', display: 'flex', alignItems: 'center', gap: '4px' }}>
          {removeButton}
          {!isExpanded && (
            <ExpandMore
              fontSize='medium'
              color='action'
              onClick={() => setIsExpanded(prev => !prev)}
            />
          )}
          {isExpanded && (
            <ExpandLess
              fontSize='medium'
              color='action'
              onClick={() => setIsExpanded(prev => !prev)}
            />
          )}
        </span>
      </div>

      {/* Content area */}
      {isExpanded && (
        <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
          {properties.map(items => (
            <div key={items.name} className='property-wrapper field-content'>
              {items.content}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

const CustomErrorFieldTemplate: React.FC<FieldErrorProps> = ({ errors }) => {
  if (!errors?.length) return null;

  return (
    <div>
      <ul className='error-detail bs-callout bs-callout-info'>
        {errors.map(error => (
          <li
            key={error.toString()}
            className='text-danger'
            style={customListItemStyles}
          >
            {error}
          </li>
        ))}
      </ul>
    </div>
  );
};

const CustomArrayFieldItemTemplate: React.FC<ArrayFieldTemplateItemType> = ({
  children,
  disabled,
  hasRemove,
  hasMoveDown,
  hasMoveUp,
  index,
  onDropIndexClick,
  onReorderClick,
  readonly,
  className,
}) => {
  const itemStyle = {
    display: 'flex',
    alignItems: 'flex-start',
    gap: '8px',
    padding: '8px',
    border: '1px solid #e0e0e0',
    borderRadius: '8px',
    marginBottom: '8px',
    backgroundColor: '#fafafa',
    position: 'relative' as const,
  };

  const buttonStyle = {
    minWidth: 'auto',
    padding: '4px',
  };


  const removeButton = hasRemove ? (
    <IconButton
      style={buttonStyle}
      onClick={onDropIndexClick(index)}
      disabled={disabled || readonly}
      size="small"
      title="Remove"
      // color="error"
    >
      <DeleteOutlineOutlined fontSize="small" sx={{ color: '#48464A' }} />
    </IconButton>
  ) : null;

  const moveUpButton = (
    <IconButton
      style={buttonStyle}
      onClick={hasMoveUp ? onReorderClick(index, index - 1) : undefined}
      disabled={disabled || readonly || !hasMoveUp}
      size="small"
      title="Move Up"
    >
      <NorthOutlined fontSize="small" sx={{ color: (disabled || readonly || !hasMoveUp) ? '#48464A' :  '#27725B' }} />
    </IconButton>
  );

  const moveDownButton = (
    <IconButton
      style={buttonStyle}
      onClick={hasMoveDown ? onReorderClick(index, index + 1) : undefined}
      disabled={disabled || readonly || !hasMoveDown}
      size="small"
      title="Move Down"
    >
      <SouthOutlined fontSize="small" sx={{ color: (disabled || readonly || !hasMoveDown) ? '#48464A' : '#27725B' }}/>
    </IconButton>
  );

  return (
    <ArrayItemContext.Provider value={{ removeButton, moveUpButton, moveDownButton }}>
      <Box style={itemStyle} className={className}>
        {/* CustomObjectFieldTemplate content with all buttons integrated in header */}
        <Box style={{ flex: 1 }}>
          {children}
        </Box>
      </Box>
    </ArrayItemContext.Provider>
  );
};

const CustomArrayFieldTemplate: React.FC<ArrayFieldTemplateProps> = ({
  canAdd,
  disabled,
  items,
  onAddClick,
  readonly,
}) => {
  const addButtonStyle = {
    marginTop: '16px',
    textTransform: 'none' as const,
    fontSize: '14px',
    padding: '8px 16px',
    borderRadius: '8px',
    backgroundColor: '#005F80',
    color: '#FAFAFA',
  };

  // console.log('ArrayFieldTemplate items:', items);

  return (
    <Box>
      {/* Array items - render each item with CustomArrayFieldItemTemplate */}
      {items && items.map((itemProps) => (
        <CustomArrayFieldItemTemplate
          key={itemProps.key}
          {...itemProps}
        />
      ))}

      {/* Add button */}
      {canAdd && (
        <Button
          style={addButtonStyle}
          onClick={onAddClick}
          disabled={disabled || readonly}
          endIcon={<Add />}
          variant="outlined"
        >
          Add Card
        </Button>
      )}
    </Box>
  );
};

export {
  CustomFieldTemplate,
  CustomObjectFieldTemplate,
  CustomErrorFieldTemplate,
  CustomArrayFieldItemTemplate,
  CustomArrayFieldTemplate,
};
